<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kalender PT. Putera Wibowo Borneo</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <?php echo app('Illuminate\Foundation\Vite')([
        'resources/css/app.css',
        'resources/css/calender.css',
        'resources/js/app.js'
    ]); ?>
</head>
<style>
    html {
        background: none;
    }

    body {
        padding: 2%;
        display: flex;
        flex-direction: column;
        /* min-height: 100%; */
        /* full viewport height */
        background-image:
            linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 80%),
            url('<?php echo e(asset('images/bg.png')); ?>');
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
    }

    .header {
        flex-shrink: 0;
    }
</style>

<body>

    <div class="page-container">
        <div class="row">
            <div class="col-4 flex items-center justify-center">
                <div class="month-navigation flex items-center space-x-4">
                    <span class="arrow text-4xl cursor-pointer">&lt;</span>
                    <h2 class="text-xlarge font-bold">AGUSTUS 2025</h2>
                    <span class="arrow text-4xl cursor-pointer">&gt;</span>
                </div>
            </div>
            <div class="col-8">
                <div class="div1 header">
                    <div class="right-logo">
                        <div class="logo-line">
                            <img src="<?php echo e(asset('images/logo.png')); ?>" alt="Logo" />
                            <p class="slogan">PT. PUTERA WIBOWO BORNEO</p>
                        </div>
                        <div class="tagline">"Mitra Terpercaya dalam Solusi Alat Berat & Layanan Teknologi Industri"
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <main class="calendar-layout mt-10">
            <section class="main-calendar-panel">
                <div class="main-calendar-grid">
                    <div class="day-name">Minggu</div>
                    <div class="day-name">Senin</div>
                    <div class="day-name">Selasa</div>
                    <div class="day-name">Rabu</div>
                    <div class="day-name">Kamis</div>
                    <div class="day-name">Jumat</div>
                    <div class="day-name">Sabtu</div>

                    <div class="date-cell inactive" data-date="29" data-month="07">29</div>
                    <div class="date-cell inactive" data-date="30" data-month="07">30</div>
                    <div class="date-cell clickable" data-date="01" data-month="08">01</div>
                    <div class="date-cell clickable" data-date="02" data-month="08">02</div>
                    <div class="date-cell clickable" data-date="03" data-month="08">03</div>
                    <div class="date-cell clickable" data-date="04" data-month="08">04</div>
                    <div class="date-cell clickable" data-date="05" data-month="08">05</div>

                    <div class="date-cell sunday clickable" data-date="06" data-month="08">06</div>
                    <div class="date-cell clickable" data-date="07" data-month="08">07</div>
                    <div class="date-cell clickable" data-date="08" data-month="08">08</div>
                    <div class="date-cell clickable" data-date="09" data-month="08">09</div>
                    <div class="date-cell clickable" data-date="10" data-month="08">10</div>
                    <div class="date-cell clickable" data-date="11" data-month="08">11</div>
                    <div class="date-cell clickable" data-date="12" data-month="08">12</div>

                    <div class="date-cell sunday clickable" data-date="13" data-month="08">13</div>
                    <div class="date-cell clickable" data-date="14" data-month="08">14</div>
                    <div class="date-cell clickable" data-date="15" data-month="08">15</div>
                    <div class="date-cell clickable" data-date="16" data-month="08">16</div>
                    <div class="date-cell holiday clickable" data-date="17" data-month="08">17</div>
                    <div class="date-cell clickable" data-date="18" data-month="08">18</div>
                    <div class="date-cell clickable" data-date="19" data-month="08">19</div>

                    <div class="date-cell sunday clickable" data-date="20" data-month="08">20</div>
                    <div class="date-cell clickable" data-date="21" data-month="08">21</div>
                    <div class="date-cell clickable" data-date="22" data-month="08">22</div>
                    <div class="date-cell clickable" data-date="23" data-month="08">23</div>
                    <div class="date-cell clickable" data-date="24" data-month="08">24</div>
                    <div class="date-cell highlighted clickable" data-date="25" data-month="08">25</div>
                    <div class="date-cell clickable" data-date="26" data-month="08">26</div>

                    <div class="date-cell sunday clickable" data-date="27" data-month="08">27</div>
                    <div class="date-cell clickable" data-date="28" data-month="08">28</div>
                    <div class="date-cell clickable" data-date="29" data-month="08">29</div>
                    <div class="date-cell clickable" data-date="30" data-month="08">30</div>
                    <div class="date-cell clickable" data-date="31" data-month="08">31</div>
                    <div class="date-cell inactive" data-date="01" data-month="09">01</div>
                    <div class="date-cell inactive" data-date="02" data-month="09">02</div>
                </div>
                <p class="holiday-note">17 Agustus 2025 : Hari Kemerdekaan Republik Indonesia</p>

                <!-- Activity Display Section -->
                <div id="activityDisplay" class="activity-display" style="display: none;">
                    <div class="activity-header">
                        <h3 id="selectedDateTitle">Kegiatan untuk tanggal</h3>
                        <button id="closeActivityBtn" class="close-activity-btn">&times;</button>
                    </div>
                    <div id="activityList" class="activity-list">
                        <!-- Activities will be populated here by JavaScript -->
                    </div>
                </div>
            </section>

            <aside class="side-panel">

                <div class="mini-calendar">
                    <h3>Juli 2025</h3>
                    <div class="mini-grid">
                        <span>M</span><span>S</span><span>S</span><span>R</span><span>K</span><span>J</span><span>S</span>
                        <span class="inactive">29</span><span
                            class="inactive">30</span><span>01</span><span>02</span><span>03</span><span>04</span><span
                            class="sunday">05</span>
                        <span
                            class="sunday">06</span><span>07</span><span>08</span><span>09</span><span>10</span><span>11</span><span>12</span>
                        <span
                            class="sunday">13</span><span>14</span><span>15</span><span>16</span><span>17</span><span>18</span><span>19</span>
                        <span
                            class="sunday">20</span><span>21</span><span>22</span><span>23</span><span>24</span><span>25</span><span>26</span>
                        <span class="sunday">27</span><span>28</span><span>29</span><span>30</span><span>31</span><span
                            class="inactive">01</span><span class="inactive">02</span>
                    </div>
                </div>

                <div class="reminder-form-section">
                    <div class="reminder-form-container">
                        <!-- Container semua reminder form -->
                        <div id="reminderForms">
                            <!-- Form pertama -->
                            <form id="reminderForm"
                                class="reminder-form space-y-4 p-4 rounded-lg shadow mb-4 dark:bg-gray-800">
                                <p class="form-title">Pengingat Tugas/Kegiatan</p>
                                <div>
                                    <input type="text" id="activityName" name="activityName"
                                        class="bg-transparent w-full px-3 py-2 border rounded-md text-sm text-dark"
                                        placeholder="Masukkan Kegiatan" required />
                                </div>
                                <div>
                                    <input type="date" id="activityDate" name="activityDate"
                                        class="bg-transparent w-full px-3 py-2 border rounded-md text-sm text-dark"
                                        required />
                                </div>
                                <div>
                                    <div class="flex justify-end mb-3">
                                        <input type="time" id="activityTime" name="activityTime"
                                            class="bg-transparent w-full px-3 py-2 border rounded-md text-sm text-dark"
                                            required />
                                        <button type="submit" id="addReminderBtn"
                                            class="text-white bg-blue-400 hover:bg-blue-800 px-4 py-2 rounded-lg text-lg font-bold ml-10">+</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Reminder List Display -->
                <div class="reminder-list-section">
                    <h3 class="reminder-list-title">List Reminder</h3>
                    <div id="reminderListDisplay" class="reminder-list-container">
                        <!-- Pre-populated reminders will be displayed here -->
                    </div>
                </div>

            </aside>

        </main>

        <a href="<?php echo e(route('home')); ?>" class="home-button neumorphism">
            <img class="imgicon-purple" src="<?php echo e(asset('assets/icon/home.png')); ?>" alt="Home" width="40" height="40">
            </img>
            <span>HOME</span>
        </a>
    </div>

    <!-- Enhanced Calendar Styles -->
    <style>
        /* Fix desktop layout overflow issues */
        .calendar-layout {
            display: flex;
            gap: clamp(0.8rem, 1.5vw, 1.5rem);
            max-height: calc(100vh - 8rem);
            overflow: hidden;
            width: 100%;
            box-sizing: border-box;
        }

        .main-calendar-panel {
            flex: 1;
            min-width: 0;
            /* Prevents flex item from overflowing */
            max-width: calc(70vw - 2rem);
        }

        .side-panel {
            flex: 0 0 clamp(280px, 28vw, 350px);
            max-width: clamp(280px, 28vw, 350px);
            overflow-y: auto;
            max-height: 100%;
        }

        /* Enhanced date cell styling for better visual hierarchy */
        .date-cell {
            font-size: clamp(1rem, 1.8vw, 1.4rem);
            font-weight: 500;
            color: var(--dark-blue);
            padding: clamp(0.3rem, 0.8vw, 0.6rem);
            display: flex;
            justify-content: center;
            align-items: center;
            height: clamp(40px, 7vw, 55px);
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
        }

        .date-cell.clickable {
            cursor: pointer;
            user-select: none;
        }

        .date-cell.clickable:hover {
            background-color: rgba(108, 86, 123, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .date-cell.clickable:active {
            transform: translateY(0);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        .date-cell.selected {
            background-color: var(--purple);
            color: white;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(108, 86, 123, 0.4);
        }

        .date-cell.highlighted {
            background-color: #fff;
            color: var(--dark-blue);
            border-radius: 50%;
            width: clamp(40px, 7vw, 55px);
            height: clamp(40px, 7vw, 55px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
            font-weight: 600;
            justify-self: center;
            border: 2px solid var(--purple);
        }

        .date-cell.highlighted.clickable:hover {
            background-color: var(--purple);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(108, 86, 123, 0.3);
        }

        /* Reminder List Styling */
        .reminder-list-section {
            margin-bottom: 1.5rem;
        }

        .reminder-list-title {
            color: var(--purple);
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            text-align: center;
            border-bottom: 2px solid rgba(108, 86, 123, 0.2);
            padding-bottom: 0.5rem;
        }

        .reminder-list-container {
            max-height: 200px;
            overflow-y: auto;
            padding-right: 0.5rem;
        }

        .reminder-item {
            background: rgba(255, 255, 255, 0.6);
            border-radius: 8px;
            padding: 0.4rem;
            margin-bottom: 0.6rem;
            border-left: 3px solid var(--purple);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
            font-size: 0.85rem;
            line-height: 1.4;
        }

        .reminder-item:hover {
            transform: translateX(3px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .reminder-item:last-child {
            margin-bottom: 0;
        }

        .reminder-number {
            color: var(--purple);
            font-weight: 600;
            margin-right: 0.5rem;
        }

        .reminder-content {
            color: var(--dark-blue);
            font-weight: 500;
        }

        .reminder-date-time {
            color: var(--purple);
            font-size: 0.75rem;
            margin-top: 0.2rem;
            font-weight: 500;
        }

        /* Form section styling */
        .reminder-form-section {
            margin-top: 1rem;
        }

        .form-title {
            color: var(--purple);
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 0.8rem;
        }

        /* Activity display styling */
        .activity-display {
            margin-top: 2rem;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .activity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid rgba(108, 86, 123, 0.2);
        }

        .activity-header h3 {
            color: var(--purple);
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
        }

        .close-activity-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--purple);
            cursor: pointer;
            padding: 0.2rem 0.5rem;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .close-activity-btn:hover {
            background-color: rgba(108, 86, 123, 0.1);
            transform: scale(1.1);
        }

        .activity-list {
            min-height: 100px;
        }

        .activity-item {
            background: rgba(255, 255, 255, 0.6);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 0.8rem;
            border-left: 4px solid var(--purple);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
        }

        .activity-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .activity-item:last-child {
            margin-bottom: 0;
        }

        .activity-time {
            color: var(--purple);
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 0.3rem;
        }

        .activity-name {
            color: var(--dark-blue);
            font-size: 1rem;
            font-weight: 500;
        }

        .no-activities {
            text-align: center;
            color: var(--light-grey);
            font-style: italic;
            padding: 2rem;
            font-size: 1rem;
        }

        /* Enhanced day names */
        .day-name {
            color: var(--purple);
            font-weight: 700;
            font-size: 1rem;
            margin-bottom: 1.2rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Responsive adjustments for enhanced styling */
        @media (max-width: 480px) {
            .calendar-layout {
                flex-direction: column;
                gap: 1rem;
                max-height: none;
            }

            .side-panel {
                max-width: 100%;
                flex: none;
            }

            .main-calendar-panel {
                max-width: 100%;
            }

            .date-cell,
            .date-cell.highlighted {
                font-size: 1.1rem;
                height: 45px;
                padding: 0.4rem;
            }

            .date-cell.highlighted {
                width: 45px;
                height: 45px;
            }

            .activity-display {
                padding: 1rem;
                margin-top: 1.5rem;
            }

            .activity-header h3 {
                font-size: 1rem;
            }

            .reminder-list-container {
                max-height: 150px;
            }

            .reminder-item {
                font-size: 0.8rem;
                padding: 0.6rem;
            }
        }

        @media (max-width: 1024px) and (min-width: 481px) {
            .calendar-layout {
                flex-direction: column;
                gap: 1rem;
                max-height: none;
            }

            .side-panel {
                max-width: 100%;
                flex: none;
                order: 2;
            }

            .main-calendar-panel {
                max-width: 100%;
                order: 1;
            }

            .main-calendar-grid {
                gap: 0.4rem 0.8rem;
            }
        }

        /* Desktop layout optimizations */
        @media (min-width: 1025px) {
            .calendar-layout {
                gap: clamp(1rem, 2vw, 2rem);
                max-height: calc(100vh - 6rem);
            }

            .main-calendar-panel {
                max-width: calc(65vw - 1rem);
            }

            .side-panel {
                flex: 0 0 clamp(300px, 32vw, 380px);
                max-width: clamp(300px, 32vw, 380px);
            }

            .main-calendar-grid {
                gap: clamp(0.5rem, 1vw, 1rem);
            }

            .reminder-list-container {
                max-height: 220px;
            }
        }

        /* Large desktop optimizations */
        @media (min-width: 1400px) {
            .calendar-layout {
                max-height: calc(100vh - 5rem);
            }

            .main-calendar-panel {
                max-width: calc(68vw - 1rem);
            }

            .side-panel {
                flex: 0 0 clamp(320px, 30vw, 400px);
                max-width: clamp(320px, 30vw, 400px);
            }
        }
    </style>

    <!-- Interactive JavaScript Functionality -->
    <script>
        // Sample activity data - in a real application, this would come from a database
        const sampleActivities = {
            '08-01': [
                { time: '09:00', name: 'Rapat Tim Proyek' },
                { time: '14:00', name: 'Presentasi Klien' }
            ],
            '08-05': [
                { time: '10:30', name: 'Training Karyawan Baru' },
                { time: '15:00', name: 'Review Dokumen Teknis' }
            ]
        };

        // Reminder list data - includes important dates and holidays
        let reminderList = [
            {
                id: 1,
                name: 'Meeting dengan Vendor',
                date: '2025-08-12',
                time: '08:20',
                type: 'meeting'
            },
            {
                id: 2,
                name: 'Audit Internal',
                date: '2025-08-20',
                time: '09:00',
                type: 'meeting'
            }
        ];

        // Function to format date for display
        function formatDateForDisplay(dateStr) {
            const date = new Date(dateStr);
            const months = [
                'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
                'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
            ];
            return `${date.getDate()} ${months[date.getMonth()]} ${date.getFullYear()}`;
        }

        // Function to render reminder list
        function renderReminderList() {
            const reminderListContainer = document.getElementById('reminderListDisplay');

            if (reminderList.length === 0) {
                reminderListContainer.innerHTML = '<div class="reminder-item">Belum ada reminder yang ditambahkan</div>';
                return;
            }

            // Sort reminders by date
            const sortedReminders = [...reminderList].sort((a, b) => new Date(a.date) - new Date(b.date));

            reminderListContainer.innerHTML = sortedReminders.map((reminder, index) => `
                <div class="reminder-item">
                    <span class="reminder-number">${index + 1}.${reminder.name}</span>
                    <div class="reminder-date-time">${formatDateForDisplay(reminder.date)} Pukul ${reminder.time}</div>
                </div>
            `).join('');
        }

        document.addEventListener('DOMContentLoaded', function () {
            const clickableDates = document.querySelectorAll('.date-cell.clickable');
            const activityDisplay = document.getElementById('activityDisplay');
            const selectedDateTitle = document.getElementById('selectedDateTitle');
            const activityList = document.getElementById('activityList');
            const closeActivityBtn = document.getElementById('closeActivityBtn');
            const reminderForm = document.getElementById('reminderForm');

            // Initialize reminder list display
            renderReminderList();

            // Add click event listeners to all clickable date cells
            clickableDates.forEach(dateCell => {
                dateCell.addEventListener('click', function () {
                    // Remove previous selection
                    document.querySelectorAll('.date-cell.selected').forEach(cell => {
                        cell.classList.remove('selected');
                    });

                    // Add selection to clicked date
                    this.classList.add('selected');

                    // Get date information
                    const date = this.getAttribute('data-date');
                    const month = this.getAttribute('data-month');
                    const dateKey = `${month}-${date.padStart(2, '0')}`;

                    // Update title
                    const monthNames = {
                        '08': 'Agustus',
                        '07': 'Juli',
                        '09': 'September'
                    };
                    selectedDateTitle.textContent = `Kegiatan untuk ${date} ${monthNames[month] || 'Agustus'} 2025`;

                    // Get activities for this date
                    const activities = sampleActivities[dateKey] || [];

                    // Populate activity list
                    if (activities.length > 0) {
                        activityList.innerHTML = activities.map(activity => `
                            <div class="activity-item">
                                <div class="activity-time">${activity.time}</div>
                                <div class="activity-name">${activity.name}</div>
                            </div>
                        `).join('');
                    } else {
                        activityList.innerHTML = '<div class="no-activities">Tidak ada kegiatan terjadwal untuk tanggal ini</div>';
                    }

                    // Show activity display
                    activityDisplay.style.display = 'block';

                    // Smooth scroll to activity display
                    setTimeout(() => {
                        activityDisplay.scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest'
                        });
                    }, 100);
                });
            });

            // Close activity display
            closeActivityBtn.addEventListener('click', function () {
                activityDisplay.style.display = 'none';
                // Remove selection from all dates
                document.querySelectorAll('.date-cell.selected').forEach(cell => {
                    cell.classList.remove('selected');
                });
            });

            // Handle reminder form submission
            reminderForm.addEventListener('submit', function (e) {
                e.preventDefault();

                const activityName = document.getElementById('activityName').value.trim();
                const activityDate = document.getElementById('activityDate').value;
                const activityTime = document.getElementById('activityTime').value;

                if (activityName && activityDate && activityTime) {
                    // Add new reminder to the list
                    const newReminder = {
                        id: reminderList.length + 1,
                        name: activityName,
                        date: activityDate,
                        time: activityTime,
                        type: 'user-added'
                    };

                    reminderList.push(newReminder);

                    // Re-render the reminder list
                    renderReminderList();

                    // Clear the form
                    reminderForm.reset();

                    // Show success feedback (optional)
                    const button = document.getElementById('addReminderBtn');
                    const originalText = button.textContent;
                    button.textContent = '✓';
                    button.style.backgroundColor = '#22c55e';

                    setTimeout(() => {
                        button.textContent = originalText;
                        button.style.backgroundColor = '';
                    }, 1500);
                }
            });

            // Add visual feedback for dates with activities
            function updateActivityIndicators() {
                // Clear existing indicators
                document.querySelectorAll('.activity-indicator').forEach(indicator => {
                    indicator.remove();
                });

                // Add indicators for dates with activities
                Object.keys(sampleActivities).forEach(dateKey => {
                    const [month, date] = dateKey.split('-');
                    const dateCell = document.querySelector(`[data-date="${parseInt(date)}"][data-month="${month}"]`);
                    if (dateCell && dateCell.classList.contains('clickable')) {
                        // Add a small indicator for dates with activities
                        const indicator = document.createElement('div');
                        indicator.className = 'activity-indicator';
                        indicator.style.cssText = `
                            position: absolute;
                            top: 5px;
                            right: 5px;
                            width: 6px;
                            height: 6px;
                            background-color: var(--red);
                            border-radius: 50%;
                            box-shadow: 0 1px 3px rgba(0,0,0,0.3);
                        `;
                        dateCell.style.position = 'relative';
                        dateCell.appendChild(indicator);
                    }
                });

                // Add indicators for reminder dates
                reminderList.forEach(reminder => {
                    const reminderDate = new Date(reminder.date);
                    const month = String(reminderDate.getMonth() + 1).padStart(2, '0');
                    const date = reminderDate.getDate();

                    const dateCell = document.querySelector(`[data-date="${date}"][data-month="${month}"]`);
                    if (dateCell && dateCell.classList.contains('clickable')) {
                        // Check if indicator already exists
                        if (!dateCell.querySelector('.activity-indicator')) {
                            const indicator = document.createElement('div');
                            indicator.className = 'activity-indicator';
                            indicator.style.cssText = `
                                position: absolute;
                                top: 5px;
                                right: 5px;
                                width: 6px;
                                height: 6px;
                                background-color: var(--purple);
                                border-radius: 50%;
                                box-shadow: 0 1px 3px rgba(0,0,0,0.3);
                            `;
                            dateCell.style.position = 'relative';
                            dateCell.appendChild(indicator);
                        }
                    }
                });
            }

            // Initial call to set up indicators
            updateActivityIndicators();

            // Update indicators when new reminders are added
            const originalRenderReminderList = renderReminderList;
            renderReminderList = function () {
                originalRenderReminderList();
                updateActivityIndicators();
            };
        });
    </script>
</body>

</html><?php /**PATH C:\xampp\htdocs\pwbapp\resources\views/kalender.blade.php ENDPATH**/ ?>