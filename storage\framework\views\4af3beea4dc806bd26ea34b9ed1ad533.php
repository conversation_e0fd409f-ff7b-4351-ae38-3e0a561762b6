<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pending PO - PT. PUTERA WIBOWO BORNEO</title>
    <?php echo app('Illuminate\Foundation\Vite')([
        'resources/css/app.css'
    ]); ?>
    <style>
        html, body {
            height: 100vh;
            overflow: hidden;
            margin: 0;
            padding: 0;
        }

        body {
            display: flex;
            flex-direction: column;
            background-image:
                linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 80%),
                url('<?php echo e(asset('images/bg.png')); ?>');
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            padding: clamp(0.3rem, 1vw, 0.8rem);
        }

        /* Custom Grid System */
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -0.5rem;
        }

        .col, .col-6 {
            padding: 0 0.5rem;
        }

        .col-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }

        .col {
            flex: 1;
        }

        .header {
            flex-shrink: 0;
            width: 100%;
            max-width: 100%;
            padding: clamp(0.5rem, 2vw, 1rem);
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            padding: clamp(0.5rem, 1vw, 1rem);
        }

        .pending-po-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: clamp(10px, 2vw, 20px);
            padding: clamp(0.5rem, 2vw, 1.5rem);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            height: 100%;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .pending-po-header {
            text-align: center;
            margin-bottom: clamp(0.5rem, 2vw, 1rem);
            flex-shrink: 0;
        }

        .pending-po-title {
            color: var(--purple);
            font-size: clamp(1.2rem, 3vw, 2rem);
            font-weight: bold;
            margin-bottom: clamp(0.3rem, 1vw, 0.5rem);
        }

        .total-amount {
            color: var(--purple);
            font-size: clamp(1rem, 2.5vw, 1.5rem);
            font-weight: bold;
            margin-bottom: clamp(0.5rem, 1.5vw, 1rem);
        }

        .search-container {
            margin-bottom: clamp(0.5rem, 1.5vw, 1rem);
            flex-shrink: 0;
        }

        .search-input {
            width: 100%;
            padding: clamp(0.3rem, 1vw, 0.5rem);
            border: 2px solid #e0e7ff;
            border-radius: clamp(5px, 1vw, 10px);
            font-size: clamp(0.8rem, 2vw, 1rem);
            background: rgba(255, 255, 255, 0.9);
        }

        .table-container {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .table-wrapper {
            flex: 1;
            overflow: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            font-size: clamp(0.6rem, 1.5vw, 0.8rem);
            margin-bottom: 0;
            background-color: white;
        }

        .table th {
            background-color: var(--purple);
            color: white;
            font-weight: bold;
            padding: clamp(0.3rem, 1vw, 0.5rem);
            border: none;
            position: sticky;
            top: 0;
            z-index: 10;
            text-align: left;
        }

        .table td {
            padding: clamp(0.2rem, 0.8vw, 0.4rem);
            vertical-align: middle;
            border-bottom: 1px solid #e0e7ff;
        }

        .table tbody tr:hover {
            background-color: #f8f9ff;
        }

        .table tbody tr:nth-child(even) {
            background-color: #fafbff;
        }

        .table tbody tr:nth-child(odd) {
            background-color: white;
        }

        .status-badge {
            padding: clamp(0.1rem, 0.5vw, 0.2rem) clamp(0.3rem, 1vw, 0.5rem);
            border-radius: clamp(3px, 0.5vw, 5px);
            font-size: clamp(0.5rem, 1.2vw, 0.7rem);
            font-weight: bold;
        }

        .status-ready {
            background-color: #e8f5e8;
            color: #2e7d32;
        }

        .status-diajukan {
            background-color: #fff3e0;
            color: #f57c00;
        }

        .pagination-container {
            flex-shrink: 0;
            margin-top: clamp(0.5rem, 1vw, 1rem);
            text-align: center;
        }

        /* Custom Pagination */
        .pagination {
            display: inline-flex;
            list-style: none;
            margin: 0;
            padding: 0;
            gap: clamp(2px, 0.3vw, 4px);
        }

        .pagination li {
            display: inline-block;
        }

        .pagination a, .pagination span {
            display: block;
            padding: clamp(0.2rem, 0.5vw, 0.3rem) clamp(0.4rem, 1vw, 0.6rem);
            border-radius: clamp(3px, 0.5vw, 5px);
            font-size: clamp(0.6rem, 1.2vw, 0.8rem);
            text-decoration: none;
            color: var(--purple);
            background-color: white;
            border: 1px solid #e0e7ff;
            transition: all 0.2s ease;
        }

        .pagination a:hover {
            background-color: var(--purple);
            color: white;
        }

        .pagination .current {
            background-color: var(--purple);
            color: white;
            border-color: var(--purple);
        }

        .pagination .disabled {
            color: #ccc;
            cursor: not-allowed;
        }

        .pagination .disabled:hover {
            background-color: white;
            color: #ccc;
        }

        /* Utility Classes */
        .text-center {
            text-align: center;
        }

        .py-4 {
            padding-top: 1rem;
            padding-bottom: 1rem;
        }

        .hidden {
            display: none;
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .table-responsive {
                font-size: clamp(0.5rem, 2vw, 0.7rem);
            }
            
            .table th, .table td {
                padding: clamp(0.1rem, 0.5vw, 0.3rem);
            }
        }
    </style>
</head>

<body>
    <!-- Header -->
    <div class="header">
        <div class="right-logo">
            <div class="logo-line">
                <img src="<?php echo e(asset('images/logo.png')); ?>" alt="Logo" />
                <p class="slogan">PT. PUTERA WIBOWO BORNEO</p>
            </div>
            <div class="tagline">"Mitra Terpercaya dalam Solusi Alat Berat & Layanan Teknologi Industri"</div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="pending-po-container">
            <div class="pending-po-header">
                <h1 class="pending-po-title">TOTAL Pending PO Rp. 345.000.785</h1>
            </div>

            <!-- Search -->
            <div class="search-container">
                <form method="GET" action="<?php echo e(route('pending-po')); ?>">
                    <input type="text" name="search" class="search-input" placeholder="Cari No. MR, Unit, atau Status..." 
                           value="<?php echo e($search ?? ''); ?>">
                </form>
            </div>

            <!-- Table Container -->
            <div class="table-container">
                <div class="table-wrapper">
                    <table id="pendingPOTable" class="table">
                        <thead>
                            <tr>
                                <th>NO</th>
                                <th>NO. MR</th>
                                <th>UNIT</th>
                                <th>STATUS</th>
                                <th>TANGGAL MR</th>
                                <th>TOTAL</th>
                            </tr>
                        </thead>
                        <tbody id="pendingPOTableBody">
                            <?php $__empty_1 = true; $__currentLoopData = $pendingPOs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $po): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr data-search="<?php echo e(strtolower($po['no_mr'] . ' ' . $po['unit'] . ' ' . $po['status'])); ?>">
                                <td><?php echo e($index + 1); ?></td>
                                <td><strong><?php echo e($po['no_mr']); ?></strong></td>
                                <td><?php echo e($po['unit']); ?></td>
                                <td>
                                    <span class="status-badge <?php echo e($po['status'] == 'Ready WO' ? 'status-ready' : 'status-diajukan'); ?>">
                                        <?php echo e($po['status']); ?>

                                    </span>
                                </td>
                                <td><?php echo e($po['tanggal_mr']); ?></td>
                                <td><strong><?php echo e($po['total']); ?></strong></td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr id="noDataRow">
                                <td colspan="6" class="text-center py-4">
                                    <em>Tidak ada data pending PO yang ditemukan.</em>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Custom Pagination -->
                <div class="pagination-container">
                    <ul class="pagination" id="pagination">
                        <!-- Pagination will be generated by JavaScript -->
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Home Button -->
    <a href="<?php echo e(route('home')); ?>" class="card home-button neumorphism">
        <img class="imgicon-purple" src="<?php echo e(asset('assets/icon/home.png')); ?>" alt="Home" width="40" height="40">
        <span>HOME</span>
    </a>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const table = document.getElementById('pendingPOTable');
            const tbody = document.getElementById('pendingPOTableBody');
            const searchInput = document.querySelector('input[name="search"]');
            const pagination = document.getElementById('pagination');

            let currentPage = 1;
            const rowsPerPage = 10;
            let filteredRows = [];
            let allRows = [];

            // Initialize
            function init() {
                allRows = Array.from(tbody.querySelectorAll('tr:not(#noDataRow)'));
                filteredRows = [...allRows];
                updateTable();
                setupSearch();
            }

            // Setup search functionality
            function setupSearch() {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase().trim();

                    if (searchTerm === '') {
                        filteredRows = [...allRows];
                    } else {
                        filteredRows = allRows.filter(row => {
                            const searchData = row.getAttribute('data-search');
                            return searchData && searchData.includes(searchTerm);
                        });
                    }

                    currentPage = 1;
                    updateTable();
                });
            }

            // Update table display
            function updateTable() {
                // Hide all rows first
                allRows.forEach(row => row.style.display = 'none');

                // Show no data message if no filtered results
                const noDataRow = document.getElementById('noDataRow');
                if (filteredRows.length === 0) {
                    if (noDataRow) {
                        noDataRow.style.display = 'table-row';
                    }
                    pagination.innerHTML = '';
                    return;
                } else {
                    if (noDataRow) {
                        noDataRow.style.display = 'none';
                    }
                }

                // Calculate pagination
                const totalPages = Math.ceil(filteredRows.length / rowsPerPage);
                const startIndex = (currentPage - 1) * rowsPerPage;
                const endIndex = startIndex + rowsPerPage;

                // Show current page rows
                filteredRows.slice(startIndex, endIndex).forEach(row => {
                    row.style.display = 'table-row';
                });

                // Update row numbers
                filteredRows.slice(startIndex, endIndex).forEach((row, index) => {
                    const numberCell = row.querySelector('td:first-child');
                    if (numberCell) {
                        numberCell.textContent = startIndex + index + 1;
                    }
                });

                // Update pagination
                updatePagination(totalPages);
            }

            // Update pagination controls
            function updatePagination(totalPages) {
                pagination.innerHTML = '';

                if (totalPages <= 1) return;

                // Previous button
                const prevLi = document.createElement('li');
                const prevLink = document.createElement('a');
                prevLink.href = '#';
                prevLink.textContent = '‹';
                prevLink.className = currentPage === 1 ? 'disabled' : '';
                prevLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (currentPage > 1) {
                        currentPage--;
                        updateTable();
                    }
                });
                prevLi.appendChild(prevLink);
                pagination.appendChild(prevLi);

                // Page numbers
                for (let i = 1; i <= totalPages; i++) {
                    const li = document.createElement('li');
                    const link = document.createElement('span');
                    link.textContent = i;
                    link.className = i === currentPage ? 'current' : '';
                    link.style.cursor = 'pointer';

                    if (i !== currentPage) {
                        link.addEventListener('click', function() {
                            currentPage = i;
                            updateTable();
                        });
                    }

                    li.appendChild(link);
                    pagination.appendChild(li);
                }

                // Next button
                const nextLi = document.createElement('li');
                const nextLink = document.createElement('a');
                nextLink.href = '#';
                nextLink.textContent = '›';
                nextLink.className = currentPage === totalPages ? 'disabled' : '';
                nextLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (currentPage < totalPages) {
                        currentPage++;
                        updateTable();
                    }
                });
                nextLi.appendChild(nextLink);
                pagination.appendChild(nextLi);
            }

            // Initialize the table
            init();
        });
    </script>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\pwbapp\resources\views/pending-po.blade.php ENDPATH**/ ?>