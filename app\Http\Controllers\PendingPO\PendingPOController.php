<?php

namespace App\Http\Controllers\PendingPO;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class PendingPOController extends Controller
{
    /**
     * Display the pending PO list page.
     */
    public function index(Request $request)
    {
        // Sample pending PO data - in a real application, this would come from a database
        $pendingPOs = collect([
            [
                'id' => 1,
                'no_mr' => 'MR : 001/IK-IV/2025',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Ready WO',
                'tanggal_mr' => '13 Juli 2025',
                'total' => 'Rp.34.660.000'
            ],
            [
                'id' => 2,
                'no_mr' => 'MR : 001/IK-IV/2025',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Ready WO',
                'tanggal_mr' => '13 Juli 2025',
                'total' => 'Rp.34.660.000'
            ],
            [
                'id' => 3,
                'no_mr' => 'MR : 001/IK-IV/2025',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Diajukan',
                'tanggal_mr' => '13 Juli 2025',
                'total' => 'Rp.34.660.000'
            ],
            [
                'id' => 4,
                'no_mr' => 'MR : 001/IK-IV/2025',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Diajukan',
                'tanggal_mr' => '13 Juli 2025',
                'total' => 'Rp.34.660.000'
            ],
            [
                'id' => 5,
                'no_mr' => 'MR : 001/IK-IV/2025',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Diajukan',
                'tanggal_mr' => '13 Juli 2025',
                'total' => 'Rp.34.660.000'
            ],
            [
                'id' => 6,
                'no_mr' => 'MR : 001/IK-IV/2025',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Diajukan',
                'tanggal_mr' => '13 Juli 2025',
                'total' => 'Rp.34.660.000'
            ],
            [
                'id' => 7,
                'no_mr' => 'MR : 001/IK-IV/2025',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Diajukan',
                'tanggal_mr' => '13 Juli 2025',
                'total' => 'Rp.34.660.000'
            ],
            [
                'id' => 8,
                'no_mr' => 'MR : 001/IK-IV/2025',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Diajukan',
                'tanggal_mr' => '13 Juli 2025',
                'total' => 'Rp.34.660.000'
            ]
        ]);

        // Handle search functionality
        $search = $request->get('search');
        if ($search) {
            $pendingPOs = $pendingPOs->filter(function ($po) use ($search) {
                return stripos($po['no_mr'], $search) !== false ||
                       stripos($po['unit'], $search) !== false ||
                       stripos($po['status'], $search) !== false;
            });
        }

        return view('pending-po', compact('pendingPOs', 'search'));
    }
}
