<?php

namespace App\Http\Controllers\Invoice;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class InvoiceController extends Controller
{
    /**
     * Display the invoice list page.
     */
    public function index(Request $request)
    {
        // Sample invoice data - in a real application, this would come from a database
        $invoices = collect([
            [
                'id' => 1,
                'name' => '005/PWB-2025',
                'po' => '34034934',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Open',
                'tanggal_invoice' => '13 Juli 2025',
                'total' => 'Rp.34.660.000',
                'pembayaran' => 'Rp. 400.000',
                'sisa' => 'Rp. 600.000'
            ],
            [
                'id' => 2,
                'name' => '005/PWB-2025',
                'po' => '34034934',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Open',
                'tanggal_invoice' => '13 Juli 2025',
                'total' => 'Rp.34.660.000',
                'pembayaran' => 'Rp. 400.000',
                'sisa' => 'Rp. 600.000'
            ],
            [
                'id' => 3,
                'name' => '005/PWB-2025',
                'po' => '34034934',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Open',
                'tanggal_invoice' => '13 Juli 2025',
                'total' => 'Rp.34.660.000',
                'pembayaran' => 'Rp. 400.000',
                'sisa' => 'Rp. 600.000'
            ],
            [
                'id' => 4,
                'name' => '005/PWB-2025',
                'po' => '34034934',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Open',
                'tanggal_invoice' => '13 Juli 2025',
                'total' => 'Rp.34.660.000',
                'pembayaran' => 'Rp. 400.000',
                'sisa' => 'Rp. 600.000'
            ],
            [
                'id' => 5,
                'name' => '005/PWB-2025',
                'po' => '34034934',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Open',
                'tanggal_invoice' => '13 Juli 2025',
                'total' => 'Rp.34.660.000',
                'pembayaran' => 'Rp. 400.000',
                'sisa' => 'Rp. 600.000'
            ],
            [
                'id' => 6,
                'name' => '005/PWB-2025',
                'po' => '34034934',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Open',
                'tanggal_invoice' => '13 Juli 2025',
                'total' => 'Rp.34.660.000',
                'pembayaran' => 'Rp. 400.000',
                'sisa' => 'Rp. 600.000'
            ],
            [
                'id' => 7,
                'name' => '005/PWB-2025',
                'po' => '34034934',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Open',
                'tanggal_invoice' => '13 Juli 2025',
                'total' => 'Rp.34.660.000',
                'pembayaran' => 'Rp. 400.000',
                'sisa' => 'Rp. 600.000'
            ],
            [
                'id' => 8,
                'name' => '005/PWB-2025',
                'po' => '34034934',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Open',
                'tanggal_invoice' => '13 Juli 2025',
                'total' => 'Rp.34.660.000',
                'pembayaran' => 'Rp. 400.000',
                'sisa' => 'Rp. 600.000'
            ]
        ]);

        // Handle search functionality
        $search = $request->get('search');
        if ($search) {
            $invoices = $invoices->filter(function ($invoice) use ($search) {
                return stripos($invoice['name'], $search) !== false ||
                       stripos($invoice['po'], $search) !== false ||
                       stripos($invoice['unit'], $search) !== false;
            });
        }

        return view('invoice', compact('invoices', 'search'));
    }
}
